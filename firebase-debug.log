[debug] [2025-06-27T11:38:56.518Z] ----------------------------------------------------------------------
[debug] [2025-06-27T11:38:56.524Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions
[debug] [2025-06-27T11:38:56.525Z] CLI Version:   14.9.0
[debug] [2025-06-27T11:38:56.525Z] Platform:      win32
[debug] [2025-06-27T11:38:56.526Z] Node Version:  v22.14.0
[debug] [2025-06-27T11:38:56.526Z] Time:          Fri Jun 27 2025 17:08:56 GMT+0530 (India Standard Time)
[debug] [2025-06-27T11:38:56.527Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-27T11:38:57.030Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-27T11:38:57.031Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-27T11:38:57.031Z] [iam] checking project safehaven-463909 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-27T11:38:57.035Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:38:57.035Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:38:57.037Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions [none]
[debug] [2025-06-27T11:38:57.037Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions x-goog-quota-user=projects/safehaven-463909
[debug] [2025-06-27T11:38:57.038Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-27T11:38:59.329Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions 200
[debug] [2025-06-27T11:38:59.329Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
[debug] [2025-06-27T11:38:59.330Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:38:59.331Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:38:59.331Z] >>> [apiv2][query] POST https://iam.googleapis.com/v1/projects/safehaven-463909/serviceAccounts/<EMAIL>:testIamPermissions [none]
[debug] [2025-06-27T11:38:59.331Z] >>> [apiv2][body] POST https://iam.googleapis.com/v1/projects/safehaven-463909/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[debug] [2025-06-27T11:39:01.226Z] <<< [apiv2][status] POST https://iam.googleapis.com/v1/projects/safehaven-463909/serviceAccounts/<EMAIL>:testIamPermissions 200
[debug] [2025-06-27T11:39:01.227Z] <<< [apiv2][body] POST https://iam.googleapis.com/v1/projects/safehaven-463909/serviceAccounts/<EMAIL>:testIamPermissions {"permissions":["iam.serviceAccounts.actAs"]}
[info] 
[info] === Deploying to 'safehaven-463909'...
[info] 
[info] i  deploying functions 
[debug] [2025-06-27T11:39:01.244Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:01.244Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:01.245Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 [none]
[debug] [2025-06-27T11:39:01.662Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 200
[debug] [2025-06-27T11:39:01.663Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 {"projectNumber":"************","projectId":"safehaven-463909","lifecycleState":"ACTIVE","name":"Safehaven","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-06-24T09:58:04.182489Z"}
[info] i  functions: preparing codebase default for deployment 
[info] i  functions: ensuring required API cloudfunctions.googleapis.com is enabled... 
[info] i  functions: ensuring required API cloudbuild.googleapis.com is enabled... 
[info] i  artifactregistry: ensuring required API artifactregistry.googleapis.com is enabled... 
[debug] [2025-06-27T11:39:01.674Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:01.674Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:01.675Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/safehaven-463909/adminSdkConfig [none]
[debug] [2025-06-27T11:39:02.425Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/safehaven-463909/adminSdkConfig 200
[debug] [2025-06-27T11:39:02.426Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/safehaven-463909/adminSdkConfig {"projectId":"safehaven-463909","databaseURL":"https://safehaven-463909-default-rtdb.firebaseio.com","storageBucket":"safehaven-463909.firebasestorage.app"}
[debug] [2025-06-27T11:39:02.427Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:02.428Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:02.428Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs [none]
[debug] [2025-06-27T11:39:03.070Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs 200
[debug] [2025-06-27T11:39:03.071Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs {"configs":[{"name":"projects/safehaven-463909/configs/twilio"}]}
[debug] [2025-06-27T11:39:03.072Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.072Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.072Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables [none]
[debug] [2025-06-27T11:39:03.456Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables 200
[debug] [2025-06-27T11:39:03.456Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables {"variables":[{"name":"projects/safehaven-463909/configs/twilio/variables/phone_number","updateTime":"2025-06-27T11:34:40.525929174Z"},{"name":"projects/safehaven-463909/configs/twilio/variables/account_sid","updateTime":"2025-06-27T11:34:41.310800771Z"},{"name":"projects/safehaven-463909/configs/twilio/variables/auth_token","updateTime":"2025-06-27T11:34:40.655538815Z"}]}
[debug] [2025-06-27T11:39:03.457Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.457Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.458Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:03.458Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/phone_number [none]
[debug] [2025-06-27T11:39:03.460Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/account_sid [none]
[debug] [2025-06-27T11:39:03.468Z] >>> [apiv2][query] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/auth_token [none]
[debug] [2025-06-27T11:39:04.058Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/auth_token 200
[debug] [2025-06-27T11:39:04.059Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/auth_token {"name":"projects/safehaven-463909/configs/twilio/variables/auth_token","updateTime":"2025-06-27T11:34:40.655538815Z","text":"902e6a71f4aecf27df504da5fe7f1c2d"}
[debug] [2025-06-27T11:39:04.090Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/account_sid 200
[debug] [2025-06-27T11:39:04.090Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/account_sid {"name":"projects/safehaven-463909/configs/twilio/variables/account_sid","updateTime":"2025-06-27T11:34:41.310800771Z","text":"**********************************"}
[debug] [2025-06-27T11:39:04.632Z] <<< [apiv2][status] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/phone_number 200
[debug] [2025-06-27T11:39:04.633Z] <<< [apiv2][body] GET https://runtimeconfig.googleapis.com/v1beta1/projects/safehaven-463909/configs/twilio/variables/phone_number {"name":"projects/safehaven-463909/configs/twilio/variables/phone_number","updateTime":"2025-06-27T11:34:40.525929174Z","text":"+***********"}
[debug] [2025-06-27T11:39:04.636Z] Validating nodejs source
[warn] !  functions: Runtime Node.js 18 was deprecated on 2025-04-30 and will be decommissioned on 2025-10-30, after which you will not be able to deploy without upgrading. Consider upgrading now to avoid disruption. See https://cloud.google.com/functions/docs/runtime-support for full details on the lifecycle policy 
[debug] [2025-06-27T11:39:07.708Z] > [functions] package.json contents: {
  "name": "safehaven-functions",
  "description": "Cloud Functions for SafeHaven Disaster Management App",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "18"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^13.3.0",
    "firebase-functions": "^6.3.2",
    "twilio": "^4.23.0"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0"
  },
  "private": true,
  "version": "1.0.0",
  "keywords": [],
  "author": "",
  "license": "ISC"
}
[debug] [2025-06-27T11:39:07.709Z] Building nodejs source
[info] i  functions: Loading and analyzing source code for codebase default to determine what to deploy 
[debug] [2025-06-27T11:39:07.716Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-27T11:39:07.770Z] Found firebase-functions binary at 'C:\Users\<USER>\Projects\SafeHaven\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8718

[debug] [2025-06-27T11:39:10.996Z] Got response from /__/functions.yaml {"endpoints":{"sendAlertNotifications":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"eventTrigger":{"eventType":"google.firebase.database.ref.v1.created","eventFilters":{},"eventFilterPathPatterns":{"ref":"alerts/{alertId}","instance":"*"},"retry":false},"entryPoint":"sendAlertNotifications"},"cleanupExpiredAlerts":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"scheduleTrigger":{"schedule":"every 1 hours","retryConfig":{"retryCount":null,"maxDoublings":null,"maxRetrySeconds":null,"minBackoffSeconds":null,"maxBackoffSeconds":null},"timeZone":null},"entryPoint":"cleanupExpiredAlerts"},"sendSMSAlert":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"sendSMSAlert"},"sendBulkSMSAlert":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"eventTrigger":{"eventType":"google.firebase.database.ref.v1.created","eventFilters":{},"eventFilterPathPatterns":{"ref":"alerts/{alertId}","instance":"*"},"retry":false},"entryPoint":"sendBulkSMSAlert"},"handleSOSRequest":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"eventTrigger":{"eventType":"google.cloud.firestore.document.v1.created","eventFilters":{"database":"(default)","namespace":"(default)"},"eventFilterPathPatterns":{"document":"sosMessages/{sosId}"},"retry":false},"entryPoint":"handleSOSRequest"}},"specVersion":"v1alpha1","requiredAPIs":[{"api":"cloudscheduler.googleapis.com","reason":"Needed for scheduled functions."}],"extensions":{}}
[info] i  extensions: ensuring required API firebaseextensions.googleapis.com is enabled... 
[debug] [2025-06-27T11:39:15.108Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-27T11:39:15.109Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-27T11:39:15.109Z] [iam] checking project safehaven-463909 for permissions ["firebase.projects.get","firebaseextensions.instances.list"]
[debug] [2025-06-27T11:39:15.110Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:15.110Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:15.110Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions [none]
[debug] [2025-06-27T11:39:15.111Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions x-goog-quota-user=projects/safehaven-463909
[debug] [2025-06-27T11:39:15.111Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-06-27T11:39:16.988Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions 200
[debug] [2025-06-27T11:39:16.988Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909:testIamPermissions {"permissions":["firebase.projects.get","firebaseextensions.instances.list"]}
[debug] [2025-06-27T11:39:16.989Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:16.989Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:16.990Z] >>> [apiv2][query] GET https://firebaseextensions.googleapis.com/v1beta/projects/safehaven-463909/instances pageSize=100&pageToken=
[debug] [2025-06-27T11:39:18.836Z] <<< [apiv2][status] GET https://firebaseextensions.googleapis.com/v1beta/projects/safehaven-463909/instances 200
[debug] [2025-06-27T11:39:18.836Z] <<< [apiv2][body] GET https://firebaseextensions.googleapis.com/v1beta/projects/safehaven-463909/instances {}
[info] i  functions: preparing functions directory for uploading... 
[info] i  functions: packaged C:\Users\<USER>\Projects\SafeHaven\functions (65.88 KB) for uploading 
[debug] [2025-06-27T11:39:19.028Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:19.028Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:19.031Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v1/projects/safehaven-463909/locations/-/functions [none]
[debug] [2025-06-27T11:39:20.094Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v1/projects/safehaven-463909/locations/-/functions 200
[debug] [2025-06-27T11:39:20.094Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v1/projects/safehaven-463909/locations/-/functions {}
[debug] [2025-06-27T11:39:20.095Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:20.096Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:20.096Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/-/functions filter=environment%3D%22GEN_2%22
[debug] [2025-06-27T11:39:21.736Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/-/functions 200
[debug] [2025-06-27T11:39:21.736Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/-/functions {}
[debug] [2025-06-27T11:39:21.738Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:21.738Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:21.739Z] >>> [apiv2][query] GET https://firestore.googleapis.com/v1/projects/safehaven-463909/databases/(default) [none]
[debug] [2025-06-27T11:39:23.884Z] <<< [apiv2][status] GET https://firestore.googleapis.com/v1/projects/safehaven-463909/databases/(default) 200
[debug] [2025-06-27T11:39:23.884Z] <<< [apiv2][body] GET https://firestore.googleapis.com/v1/projects/safehaven-463909/databases/(default) {"name":"projects/safehaven-463909/databases/(default)","uid":"a67dbf60-be95-4f03-a5da-e40e46b899ef","createTime":"2025-06-24T11:19:39.671716Z","updateTime":"2025-06-24T11:19:39.671716Z","locationId":"nam5","type":"FIRESTORE_NATIVE","concurrencyMode":"PESSIMISTIC","versionRetentionPeriod":"3600s","earliestVersionTime":"2025-06-27T10:39:25.044757Z","appEngineIntegrationMode":"DISABLED","keyPrefix":"s","pointInTimeRecoveryEnablement":"POINT_IN_TIME_RECOVERY_DISABLED","deleteProtectionState":"DELETE_PROTECTION_DISABLED","databaseEdition":"STANDARD","freeTier":true,"etag":"IPyDlorCkY4DMObfvejii44D"}
[info] i  functions: ensuring required API cloudscheduler.googleapis.com is enabled... 
[info] i  functions: ensuring required API run.googleapis.com is enabled... 
[info] i  functions: ensuring required API eventarc.googleapis.com is enabled... 
[info] i  functions: ensuring required API pubsub.googleapis.com is enabled... 
[info] i  functions: ensuring required API storage.googleapis.com is enabled... 
[info] i  functions: generating the service identity for pubsub.googleapis.com... 
[debug] [2025-06-27T11:39:23.905Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:23.905Z] Checked if tokens are valid: true, expires at: *************
[info] i  functions: generating the service identity for eventarc.googleapis.com... 
[debug] [2025-06-27T11:39:23.907Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:23.907Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:23.908Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity [none]
[debug] [2025-06-27T11:39:23.908Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-06-27T11:39:23.908Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {}
[debug] [2025-06-27T11:39:23.911Z] >>> [apiv2][query] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity [none]
[debug] [2025-06-27T11:39:23.912Z] >>> [apiv2][(partial)header] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity x-goog-quota-user=projects/************
[debug] [2025-06-27T11:39:23.912Z] >>> [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {}
[debug] [2025-06-27T11:39:26.312Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity 200
[debug] [2025-06-27T11:39:26.313Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/pubsub.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"101671370182551232648"}}
[debug] [2025-06-27T11:39:26.314Z] <<< [apiv2][status] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity 200
[debug] [2025-06-27T11:39:26.315Z] <<< [apiv2][body] POST https://serviceusage.googleapis.com/v1beta1/projects/************/services/eventarc.googleapis.com:generateServiceIdentity {"name":"operations/finished.DONE_OPERATION","done":true,"response":{"@type":"type.googleapis.com/google.api.serviceusage.v1beta1.ServiceIdentity","email":"<EMAIL>","uniqueId":"117465125869142508695"}}
[debug] [2025-06-27T11:39:26.322Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:26.322Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:26.323Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:26.928Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:26.929Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","containerInfo":"************","consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:26.930Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","containerInfo":"************","consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:26.932Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:26.932Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:26.932Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/************:getIamPolicy [none]
[debug] [2025-06-27T11:39:28.786Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/************:getIamPolicy 200
[debug] [2025-06-27T11:39:28.786Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/************:getIamPolicy {"version":1,"etag":"BwY4XjqAajQ=","bindings":[{"role":"roles/artifactregistry.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.builds.builder","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudbuild.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudfunctions.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/cloudscheduler.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/containerregistry.ServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/editor","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/eventarc.eventReceiver","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.managementServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebase.sdkAdminServiceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasedatabase.admin","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebasemods.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firebaserules.system","members":["serviceAccount:<EMAIL>"]},{"role":"roles/firestore.serviceAgent","members":["serviceAccount:<EMAIL>"]},{"role":"roles/iam.serviceAccountTokenCreator","members":["serviceAccount:<EMAIL>","serviceAccount:<EMAIL>"]},{"role":"roles/owner","members":["user:<EMAIL>"]},{"role":"roles/run.invoker","members":["serviceAccount:<EMAIL>"]},{"role":"roles/run.serviceAgent","members":["serviceAccount:<EMAIL>"]}]}
[debug] [2025-06-27T11:39:28.789Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:28.790Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:28.790Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 [none]
[debug] [2025-06-27T11:39:29.380Z] <<< [apiv2][status] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 200
[debug] [2025-06-27T11:39:29.380Z] <<< [apiv2][body] GET https://cloudresourcemanager.googleapis.com/v1/projects/safehaven-463909 {"projectNumber":"************","projectId":"safehaven-463909","lifecycleState":"ACTIVE","name":"Safehaven","labels":{"firebase":"enabled","firebase-core":"disabled"},"createTime":"2025-06-24T09:58:04.182489Z"}
[debug] [2025-06-27T11:39:29.380Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:29.381Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:29.381Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:29.780Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:29.780Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","consumer":"projects/************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:29.781Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","consumer":"projects/************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:29.781Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:29.781Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:29.782Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:30.143Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:30.143Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","consumer":"projects/************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:30.144Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"containerInfo":"************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","consumer":"projects/************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:30.145Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.145Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.145Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:30.517Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:30.517Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:30.518Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:30.518Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.519Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.520Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:30.875Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:30.875Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"service":"compute.googleapis.com","containerInfo":"************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","consumer":"projects/************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:30.876Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"service":"compute.googleapis.com","containerInfo":"************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","consumer":"projects/************","serviceTitle":"Compute Engine API"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:30.876Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.877Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:30.877Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:39:31.251Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:39:31.252Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","containerInfo":"************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:39:31.252Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","containerInfo":"************","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:39:31.257Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:31.257Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:31.257Z] >>> [apiv2][query] GET https://cloudbilling.googleapis.com/v1/projects/safehaven-463909/billingInfo [none]
[debug] [2025-06-27T11:39:32.862Z] <<< [apiv2][status] GET https://cloudbilling.googleapis.com/v1/projects/safehaven-463909/billingInfo 200
[debug] [2025-06-27T11:39:32.863Z] <<< [apiv2][body] GET https://cloudbilling.googleapis.com/v1/projects/safehaven-463909/billingInfo {"name":"projects/safehaven-463909/billingInfo","projectId":"safehaven-463909","billingAccountName":"billingAccounts/019560-37F5E6-C348FF","billingEnabled":true}
[debug] [2025-06-27T11:39:32.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:32.868Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:32.868Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions:generateUploadUrl [none]
[debug] [2025-06-27T11:39:34.718Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions:generateUploadUrl 200
[debug] [2025-06-27T11:39:34.719Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions:generateUploadUrl {"uploadUrl":"https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip?GoogleAccessId=<EMAIL>&Expires=**********&Signature=nggZJldkDbmd3zEtkHQpyvYhT7oU7vQHhsgY%2FukHi6uKBZoY9twx14cJTHrWayDPmCAiPyrdydFJItuW7mMH06%2BLol3ZFnRZlnuwHr78L%2F%2F%2BkI2bXCKVuUMHXAEtpwh7JOmxIwgEVFmbW0St1IteJIA7z4URCh9Bbqc9tTi7P3wofbtF4%2BJjIjsZqutTTO7%2BIH%2Bhfk9kFfgiYsp1zwVxx2iFV2TgElX5NXyK6pITpwjUxp%2Bog%2Fkdb%2BVsgKkdfehZY851pfmDCWJkf5JgSaj5lKVM%2FImMyyGR5HTcE3bx%2BG5fWHrN0lNfFcGBNh2wRlyRNtzcMYFLmE46AtmyrMXzww%3D%3D","storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}}
[debug] [2025-06-27T11:39:34.721Z] >>> [apiv2][query] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip GoogleAccessId=service-************%40gcf-admin-robot.iam.gserviceaccount.com&Expires=**********&Signature=nggZJldkDbmd3zEtkHQpyvYhT7oU7vQHhsgY%2FukHi6uKBZoY9twx14cJTHrWayDPmCAiPyrdydFJItuW7mMH06%2BLol3ZFnRZlnuwHr78L%2F%2F%2BkI2bXCKVuUMHXAEtpwh7JOmxIwgEVFmbW0St1IteJIA7z4URCh9Bbqc9tTi7P3wofbtF4%2BJjIjsZqutTTO7%2BIH%2Bhfk9kFfgiYsp1zwVxx2iFV2TgElX5NXyK6pITpwjUxp%2Bog%2Fkdb%2BVsgKkdfehZY851pfmDCWJkf5JgSaj5lKVM%2FImMyyGR5HTcE3bx%2BG5fWHrN0lNfFcGBNh2wRlyRNtzcMYFLmE46AtmyrMXzww%3D%3D
[debug] [2025-06-27T11:39:34.722Z] >>> [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip [stream]
[debug] [2025-06-27T11:39:36.406Z] <<< [apiv2][status] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip 200
[debug] [2025-06-27T11:39:36.406Z] <<< [apiv2][body] PUT https://storage.googleapis.com/gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com/2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip [omitted]
[info] +  functions: functions folder uploaded successfully 
[info] i  functions: creating Node.js 18 (2nd Gen) function sendAlertNotifications(us-central1)... 
[info] i  functions: creating Node.js 18 (2nd Gen) function cleanupExpiredAlerts(us-central1)... 
[info] i  functions: creating Node.js 18 (2nd Gen) function sendSMSAlert(us-central1)... 
[info] i  functions: creating Node.js 18 (2nd Gen) function sendBulkSMSAlert(us-central1)... 
[info] i  functions: creating Node.js 18 (2nd Gen) function handleSOSRequest(us-central1)... 
[debug] [2025-06-27T11:39:36.440Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:36.440Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:36.441Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions functionId=sendAlertNotifications
[debug] [2025-06-27T11:39:36.441Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/functions/sendAlertNotifications","buildConfig":{"runtime":"nodejs18","entryPoint":"sendAlertNotifications","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendAlertNotifications","FUNCTION_SIGNATURE_TYPE":"cloudevent","FUNCTION_TARGET":"sendAlertNotifications","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"eventTrigger":{"eventType":"google.firebase.database.ref.v1.created","retryPolicy":"RETRY_POLICY_DO_NOT_RETRY","eventFilters":[{"attribute":"ref","value":"alerts/{alertId}","operator":"match-path-pattern"},{"attribute":"instance","value":"*","operator":"match-path-pattern"}],"triggerRegion":"us-central1"}}
[debug] [2025-06-27T11:39:55.409Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions 400
[debug] [2025-06-27T11:39:55.409Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"error":{"code":400,"message":"Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendalertnotifications-189781: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.","status":"FAILED_PRECONDITION"}}
[warn] !  functions: Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=sendAlertNotifications had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendalertnotifications-189781: Invalid resource state for "": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role. 
[warn] !  functions: Since this is your first time using 2nd gen functions, we need a little bit longer to finish setting everything up. Retry the deployment in a few minutes. 
[warn] !  functions:  failed to create function projects/safehaven-463909/locations/us-central1/functions/sendAlertNotifications 
[error] Failed to create function projects/safehaven-463909/locations/us-central1/functions/sendAlertNotifications
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.416Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.416Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:55.416Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions functionId=cleanupExpiredAlerts
[debug] [2025-06-27T11:39:55.416Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"}}
[debug] [2025-06-27T11:39:55.417Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions functionId=sendSMSAlert
[debug] [2025-06-27T11:39:55.417Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"}}
[debug] [2025-06-27T11:39:55.423Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions functionId=sendBulkSMSAlert
[debug] [2025-06-27T11:39:55.423Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/functions/sendBulkSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendBulkSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendBulkSMSAlert","FUNCTION_SIGNATURE_TYPE":"cloudevent","FUNCTION_TARGET":"sendBulkSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"eventTrigger":{"eventType":"google.firebase.database.ref.v1.created","retryPolicy":"RETRY_POLICY_DO_NOT_RETRY","eventFilters":[{"attribute":"ref","value":"alerts/{alertId}","operator":"match-path-pattern"},{"attribute":"instance","value":"*","operator":"match-path-pattern"}],"triggerRegion":"us-central1"}}
[debug] [2025-06-27T11:39:55.428Z] >>> [apiv2][query] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions functionId=handleSOSRequest
[debug] [2025-06-27T11:39:55.429Z] >>> [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/functions/handleSOSRequest","buildConfig":{"runtime":"nodejs18","entryPoint":"handleSOSRequest","source":{"storageSource":{"bucket":"gcf-v2-uploads-************.us-central1.cloudfunctions.appspot.com","object":"2b79c89f-2093-4ff7-8d53-d8aeb22d6c82.zip"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""}},"serviceConfig":{"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/handleSOSRequest","FUNCTION_SIGNATURE_TYPE":"cloudevent","FUNCTION_TARGET":"handleSOSRequest","LOG_EXECUTION_ID":"true"},"ingressSettings":null,"timeoutSeconds":null,"serviceAccountEmail":null,"availableMemory":"256Mi","minInstanceCount":null,"maxInstanceCount":null,"maxInstanceRequestConcurrency":80,"availableCpu":"1","vpcConnector":null,"vpcConnectorEgressSettings":null},"labels":{"deployment-tool":"cli-firebase","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"eventTrigger":{"eventType":"google.cloud.firestore.document.v1.created","retryPolicy":"RETRY_POLICY_DO_NOT_RETRY","eventFilters":[{"attribute":"database","value":"(default)"},{"attribute":"namespace","value":"(default)"},{"attribute":"document","value":"sosMessages/{sosId}","operator":"match-path-pattern"}],"triggerRegion":"nam5"}}
[debug] [2025-06-27T11:39:57.444Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions 200
[debug] [2025-06-27T11:39:57.445Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2"},"done":false}
[debug] [2025-06-27T11:39:57.446Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:57.446Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:57.446Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:39:57.663Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions 200
[debug] [2025-06-27T11:39:57.664Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2"},"done":false}
[debug] [2025-06-27T11:39:57.664Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:57.664Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:57.664Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:39:58.880Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:39:58.880Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:39:59.082Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:39:59.083Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:39:59.389Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:39:59.389Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:59.389Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:59.389Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:39:59.587Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:39:59.588Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:59.588Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:39:59.588Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:00.774Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:00.775Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:00.943Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:00.944Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:01.787Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:01.788Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:01.788Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:01.788Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:01.957Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:01.957Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:01.958Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:01.958Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:03.186Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:03.186Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:03.330Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:03.331Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:05.191Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:05.192Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:05.192Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:05.192Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:05.331Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:05.332Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:05.332Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:05.332Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:06.564Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:06.564Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:06.709Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:06.710Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:10.566Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:10.567Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:10.567Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:10.567Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:10.717Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:10.717Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:10.717Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:10.717Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:11.959Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:11.959Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:12.293Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:12.293Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:12.328Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions 400
[debug] [2025-06-27T11:40:12.328Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"error":{"code":400,"message":"Validation failed for trigger projects/safehaven-463909/locations/nam5/triggers/handlesosrequest-930208: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.","status":"FAILED_PRECONDITION"}}
[warn] !  functions: Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=handleSOSRequest had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/nam5/triggers/handlesosrequest-930208: Invalid resource state for "": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role. 
[warn] !  functions: Since this is your first time using 2nd gen functions, we need a little bit longer to finish setting everything up. Retry the deployment in a few minutes. 
[warn] !  functions:  failed to create function projects/safehaven-463909/locations/us-central1/functions/handleSOSRequest 
[error] Failed to create function projects/safehaven-463909/locations/us-central1/functions/handleSOSRequest
[debug] [2025-06-27T11:40:12.601Z] <<< [apiv2][status] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions 400
[debug] [2025-06-27T11:40:12.601Z] <<< [apiv2][body] POST https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions {"error":{"code":400,"message":"Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendbulksmsalert-404702: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.","status":"FAILED_PRECONDITION"}}
[warn] !  functions: Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=sendBulkSMSAlert had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendbulksmsalert-404702: Invalid resource state for "": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role. 
[warn] !  functions: Since this is your first time using 2nd gen functions, we need a little bit longer to finish setting everything up. Retry the deployment in a few minutes. 
[warn] !  functions:  failed to create function projects/safehaven-463909/locations/us-central1/functions/sendBulkSMSAlert 
[error] Failed to create function projects/safehaven-463909/locations/us-central1/functions/sendBulkSMSAlert
[debug] [2025-06-27T11:40:19.970Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:19.971Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:19.971Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:19.972Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:20.305Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:20.305Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:20.306Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:20.306Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:21.779Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:21.779Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:22.494Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:22.494Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:31.787Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:31.788Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:31.788Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:31.788Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:32.503Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:32.503Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:32.503Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:32.503Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:33.489Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:33.490Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:34.210Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:34.210Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:43.490Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:43.491Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:43.491Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:43.491Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:44.215Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:44.215Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:44.215Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:44.216Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:45.268Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:45.269Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:46.015Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:46.015Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build in progress","state":"IN_PROGRESS","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:55.284Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:40:55.285Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:55.285Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:55.285Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:40:56.023Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:40:56.024Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:56.024Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:40:56.024Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:40:57.040Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:40:57.040Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","state":"NOT_STARTED"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE=","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:40:57.041Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE= for region us-central1
[debug] [2025-06-27T11:40:57.760Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:40:57.760Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/sendsmsalert?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ==","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:40:57.760Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ== for region us-central1
[debug] [2025-06-27T11:41:07.056Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:41:07.057Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:07.058Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:07.059Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:41:07.775Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:41:07.776Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:07.776Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:07.776Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:41:08.716Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:41:08.716Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/cleanupexpiredalerts?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE=","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:41:08.716Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE= for region us-central1
[debug] [2025-06-27T11:41:09.637Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:41:09.637Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/sendsmsalert?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ==","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:41:09.637Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ== for region us-central1
[debug] [2025-06-27T11:41:18.728Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:41:18.728Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:18.728Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:18.728Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:41:19.650Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:41:19.651Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:19.651Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:19.651Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:41:20.401Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:41:20.401Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/cleanupexpiredalerts?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE=","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:41:20.402Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE= for region us-central1
[debug] [2025-06-27T11:41:21.510Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:41:21.510Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/sendsmsalert?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ==","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:41:21.510Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ== for region us-central1
[debug] [2025-06-27T11:41:30.415Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:41:30.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:30.415Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:30.416Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:41:31.518Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:41:31.518Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:31.518Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:31.519Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:41:32.063Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:41:32.064Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/cleanupexpiredalerts?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE=","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":false}
[debug] [2025-06-27T11:41:32.064Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE= for region us-central1
[debug] [2025-06-27T11:41:33.188Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:41:33.188Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"IN_PROGRESS","resource":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/sendsmsalert?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ==","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":false}
[debug] [2025-06-27T11:41:33.188Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ== for region us-central1
[debug] [2025-06-27T11:41:42.065Z] [create-default-us-central1-cleanupExpiredAlerts] Retrying task index 0
[debug] [2025-06-27T11:41:42.066Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:42.066Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:42.067Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 [none]
[debug] [2025-06-27T11:41:43.198Z] [create-default-us-central1-sendSMSAlert] Retrying task index 0
[debug] [2025-06-27T11:41:43.199Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:43.199Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:43.199Z] >>> [apiv2][query] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 [none]
[debug] [2025-06-27T11:41:43.735Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 200
[debug] [2025-06-27T11:41:43.735Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-1751024398146-6388c233e9b63-ca9e4148-71e48dd0","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.634047430Z","endTime":"2025-06-27T11:41:41.742402389Z","target":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"COMPLETE","resource":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/cleanupexpiredalerts?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE=","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068"},"done":true,"response":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/cleanupExpiredAlerts","buildConfig":{"build":"projects/************/locations/us-central1/builds/0f62e2c4-3618-4e3a-b82b-4ff9d2c96068","runtime":"nodejs18","entryPoint":"cleanupExpiredAlerts","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"cleanupExpiredAlerts/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/cleanupExpiredAlerts","FUNCTION_TARGET":"cleanupExpiredAlerts","LOG_EXECUTION_ID":"true"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://cleanupexpiredalerts-uegoaipuiq-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"cleanupexpiredalerts-00001-gag","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-06-27T11:39:58.628035954Z","labels":{"deployment-tool":"cli-firebase","deployment-scheduled":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2","url":"https://us-central1-safehaven-463909.cloudfunctions.net/cleanupExpiredAlerts","createTime":"2025-06-27T11:39:58.628035954Z","satisfiesPzi":true}}
[debug] [2025-06-27T11:41:43.735Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy8wZjYyZTJjNC0zNjE4LTRlM2EtYjgyYi00ZmY5ZDJjOTYwNjgSe3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19jbGVhbnVwX2V4cGlyZWRfYWxlcnRzOnZlcnNpb25fMRiIjtWj6wwiTnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9jbGVhbnVwRXhwaXJlZEFsZXJ0cyoLCMqG+sIGENDUuzgyCG5vZGVqczE4OnYKI2djci5pby9nYWUtcnVudGltZXMvbm9kZWpzMTg6c3RhYmxlEk91cy1jZW50cmFsMS1kb2NrZXIucGtnLmRldi9zZXJ2ZXJsZXNzLXJ1bnRpbWVzL2dvb2dsZS0yMi1mdWxsL3J1bnRpbWVzL25vZGVqczE4QAE= for region us-central1
[debug] [2025-06-27T11:41:43.736Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:43.736Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:43.736Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:41:44.349Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:41:44.350Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","containerInfo":"************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:41:44.350Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"consumer":"projects/************","containerInfo":"************","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","serviceTitle":"Compute Engine API","service":"compute.googleapis.com"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:41:44.352Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:44.352Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:44.352Z] >>> [apiv2][query] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts:setIamPolicy [none]
[debug] [2025-06-27T11:41:44.353Z] >>> [apiv2][body] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts:setIamPolicy {"policy":{"bindings":[{"role":"roles/run.invoker","members":["serviceAccount:<EMAIL>"]}],"etag":"","version":3},"updateMask":"bindings,etag,version"}
[debug] [2025-06-27T11:41:44.862Z] <<< [apiv2][status] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 200
[debug] [2025-06-27T11:41:44.862Z] <<< [apiv2][body] GET https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8 {"name":"projects/safehaven-463909/locations/us-central1/operations/operation-*************-6388c2342dfcb-6ba0167c-5d8e32b8","metadata":{"@type":"type.googleapis.com/google.cloud.functions.v2.OperationMetadata","createTime":"2025-06-27T11:39:58.886876651Z","endTime":"2025-06-27T11:41:40.871458085Z","target":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","verb":"create","cancelRequested":false,"apiVersion":"v2","requestResource":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"ingressSettings":"ALLOW_ALL","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2"},"stages":[{"name":"BUILD","message":"Build finished","state":"COMPLETE","resource":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","resourceUri":"https://console.cloud.google.com/cloud-build/builds;region=us-central1/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1?project=************"},{"name":"SERVICE","message":"Creating Cloud Run service","state":"COMPLETE","resource":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","resourceUri":"https://console.cloud.google.com/run/detail/us-central1/sendsmsalert?project=safehaven-463909"}],"sourceToken":"Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ==","operationType":"CREATE_FUNCTION","buildName":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1"},"done":true,"response":{"@type":"type.googleapis.com/google.cloud.functions.v2.Function","name":"projects/safehaven-463909/locations/us-central1/functions/sendSMSAlert","buildConfig":{"build":"projects/************/locations/us-central1/builds/4bc58bc5-4be8-4bb4-aaf7-4cf7607bc0e1","runtime":"nodejs18","entryPoint":"sendSMSAlert","source":{"storageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"environmentVariables":{"GOOGLE_NODE_RUN_SCRIPTS":""},"dockerRepository":"projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts","sourceProvenance":{"resolvedStorageSource":{"bucket":"gcf-v2-sources-************-us-central1","object":"sendSMSAlert/function-source.zip","generation":"****************"}},"dockerRegistry":"ARTIFACT_REGISTRY","serviceAccount":"projects/safehaven-463909/serviceAccounts/<EMAIL>","automaticUpdatePolicy":{}},"serviceConfig":{"service":"projects/safehaven-463909/locations/us-central1/services/sendsmsalert","timeoutSeconds":60,"environmentVariables":{"FIREBASE_CONFIG":"{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}","GCLOUD_PROJECT":"safehaven-463909","EVENTARC_CLOUD_EVENT_SOURCE":"projects/safehaven-463909/locations/us-central1/services/sendSMSAlert","FUNCTION_TARGET":"sendSMSAlert","LOG_EXECUTION_ID":"true"},"maxInstanceCount":20,"ingressSettings":"ALLOW_ALL","uri":"https://sendsmsalert-uegoaipuiq-uc.a.run.app","serviceAccountEmail":"<EMAIL>","availableMemory":"256Mi","allTrafficOnLatestRevision":true,"revision":"sendsmsalert-00001-kev","maxInstanceRequestConcurrency":80,"availableCpu":"1"},"state":"ACTIVE","updateTime":"2025-06-27T11:39:58.879054591Z","labels":{"deployment-tool":"cli-firebase","deployment-callable":"true","firebase-functions-hash":"79ba08d3a36c20435fb4e8602609f090529fede6"},"environment":"GEN_2","url":"https://us-central1-safehaven-463909.cloudfunctions.net/sendSMSAlert","createTime":"2025-06-27T11:39:58.879054591Z","satisfiesPzi":true}}
[debug] [2025-06-27T11:41:44.863Z] Got source token Cldwcm9qZWN0cy80NDExMTQyNDg5NjgvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2J1aWxkcy80YmM1OGJjNS00YmU4LTRiYjQtYWFmNy00Y2Y3NjA3YmMwZTESdXVzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NhZmVoYXZlbi00NjM5MDkvZ2NmLWFydGlmYWN0cy9zYWZlaGF2ZW4tLTQ2MzkwOV9fdXMtLWNlbnRyYWwxX19zZW5kX3NfbV9zX2FsZXJ0OnZlcnNpb25fMRiIjtWj6wwiRnByb2plY3RzL3NhZmVoYXZlbi00NjM5MDkvbG9jYXRpb25zL3VzLWNlbnRyYWwxL2Z1bmN0aW9ucy9zZW5kU01TQWxlcnQqDAjGhvrCBhDo7puuATIIbm9kZWpzMTg6dgojZ2NyLmlvL2dhZS1ydW50aW1lcy9ub2RlanMxODpzdGFibGUST3VzLWNlbnRyYWwxLWRvY2tlci5wa2cuZGV2L3NlcnZlcmxlc3MtcnVudGltZXMvZ29vZ2xlLTIyLWZ1bGwvcnVudGltZXMvbm9kZWpzMThAAQ== for region us-central1
[debug] [2025-06-27T11:41:44.863Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:44.863Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:44.863Z] >>> [apiv2][query] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/sendsmsalert:setIamPolicy [none]
[debug] [2025-06-27T11:41:44.864Z] >>> [apiv2][body] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/sendsmsalert:setIamPolicy {"policy":{"bindings":[{"role":"roles/run.invoker","members":["allUsers"]}],"etag":"","version":3},"updateMask":"bindings,etag,version"}
[debug] [2025-06-27T11:41:46.809Z] <<< [apiv2][status] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts:setIamPolicy 200
[debug] [2025-06-27T11:41:46.809Z] <<< [apiv2][body] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/cleanupexpiredalerts:setIamPolicy {"version":1,"etag":"BwY4jCnKb1Y=","bindings":[{"role":"roles/run.invoker","members":["serviceAccount:<EMAIL>"]}]}
[debug] [2025-06-27T11:41:46.810Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:46.810Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:46.811Z] >>> [apiv2][query] GET https://compute.googleapis.com/compute/v1/projects/************ [none]
[debug] [2025-06-27T11:41:47.165Z] <<< [apiv2][status] GET https://compute.googleapis.com/compute/v1/projects/************ 403
[debug] [2025-06-27T11:41:47.166Z] <<< [apiv2][body] GET https://compute.googleapis.com/compute/v1/projects/************ {"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","consumer":"projects/************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}}
[debug] [2025-06-27T11:41:47.166Z] unable to look up default compute service account. Falling <NAME_EMAIL>. Error: {"name":"FirebaseError","children":[],"context":{"body":{"error":{"code":403,"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","errors":[{"message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","domain":"usageLimits","reason":"accessNotConfigured","extendedHelp":"https://console.developers.google.com"}],"status":"PERMISSION_DENIED","details":[{"@type":"type.googleapis.com/google.rpc.ErrorInfo","reason":"SERVICE_DISABLED","domain":"googleapis.com","metadata":{"serviceTitle":"Compute Engine API","consumer":"projects/************","service":"compute.googleapis.com","activationUrl":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************","containerInfo":"************"}},{"@type":"type.googleapis.com/google.rpc.LocalizedMessage","locale":"en-US","message":"Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Google developers console API activation","url":"https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************"}]}]}},"response":{"statusCode":403}},"exit":1,"message":"Request to https://compute.googleapis.com/compute/v1/projects/************ had HTTP Error: 403, Compute Engine API has not been used in project ************ before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/compute.googleapis.com/overview?project=************ then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry.","status":403}
[debug] [2025-06-27T11:41:47.167Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:47.167Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:47.167Z] >>> [apiv2][query] GET https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs/firebase-schedule-cleanupExpiredAlerts-us-central1 [none]
[debug] [2025-06-27T11:41:47.319Z] <<< [apiv2][status] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/sendsmsalert:setIamPolicy 200
[debug] [2025-06-27T11:41:47.319Z] <<< [apiv2][body] POST https://run.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/services/sendsmsalert:setIamPolicy {"version":1,"etag":"BwY4jCnR6hg=","bindings":[{"role":"roles/run.invoker","members":["allUsers"]}]}
[info] +  functions[sendSMSAlert(us-central1)] Successful create operation. 
[debug] [2025-06-27T11:41:48.946Z] <<< [apiv2][status] GET https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs/firebase-schedule-cleanupExpiredAlerts-us-central1 404
[debug] [2025-06-27T11:41:48.947Z] <<< [apiv2][body] GET https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs/firebase-schedule-cleanupExpiredAlerts-us-central1 {"error":{"code":404,"message":"Job not found.","status":"NOT_FOUND"}}
[debug] [2025-06-27T11:41:48.947Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:48.947Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:48.947Z] >>> [apiv2][query] POST https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs [none]
[debug] [2025-06-27T11:41:48.947Z] >>> [apiv2][body] POST https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs {"timeZone":"UTC","name":"projects/safehaven-463909/locations/us-central1/jobs/firebase-schedule-cleanupExpiredAlerts-us-central1","httpTarget":{"uri":"https://us-central1-safehaven-463909.cloudfunctions.net/cleanupExpiredAlerts","httpMethod":"POST","oidcToken":{"serviceAccountEmail":"<EMAIL>"}},"schedule":"every 1 hours","retryConfig":{"maxDoublings":null,"retryCount":null,"maxBackoffDuration":null,"minBackoffDuration":null,"maxRetryDuration":null}}
[debug] [2025-06-27T11:41:55.101Z] <<< [apiv2][status] POST https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs 200
[debug] [2025-06-27T11:41:55.101Z] <<< [apiv2][body] POST https://cloudscheduler.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/jobs {"name":"projects/safehaven-463909/locations/us-central1/jobs/firebase-schedule-cleanupExpiredAlerts-us-central1","httpTarget":{"uri":"https://us-central1-safehaven-463909.cloudfunctions.net/cleanupExpiredAlerts","httpMethod":"POST","headers":{"User-Agent":"Google-Cloud-Scheduler"},"oidcToken":{"serviceAccountEmail":"<EMAIL>","audience":"https://us-central1-safehaven-463909.cloudfunctions.net/cleanupExpiredAlerts"}},"userUpdateTime":"2025-06-27T11:41:56Z","state":"ENABLED","retryConfig":{"maxRetryDuration":"0s","minBackoffDuration":"5s","maxBackoffDuration":"3600s","maxDoublings":5},"schedule":"every 1 hours","timeZone":"UTC","attemptDeadline":"180s"}
[debug] [2025-06-27T11:41:55.101Z] created scheduler job firebase-schedule-cleanupExpiredAlerts-us-central1
[info] +  functions[cleanupExpiredAlerts(us-central1)] Successful create operation. 
[debug] [2025-06-27T11:41:55.127Z] Total Function Deployment time: 138676
[debug] [2025-06-27T11:41:55.128Z] 5 Functions Deployed
[debug] [2025-06-27T11:41:55.128Z] 3 Functions Errored
[debug] [2025-06-27T11:41:55.128Z] 0 Function Deployments Aborted
[debug] [2025-06-27T11:41:55.128Z] Average Function Deployment time: 72122
[info] 
[info] Functions deploy had errors with the following functions:
	handleSOSRequest(us-central1)
	sendAlertNotifications(us-central1)
	sendBulkSMSAlert(us-central1)
[debug] [2025-06-27T11:41:55.821Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:55.821Z] Checked if tokens are valid: true, expires at: *************
[debug] [2025-06-27T11:41:55.821Z] >>> [apiv2][query] GET https://artifactregistry.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts [none]
[debug] [2025-06-27T11:41:57.866Z] <<< [apiv2][status] GET https://artifactregistry.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts 200
[debug] [2025-06-27T11:41:57.866Z] <<< [apiv2][body] GET https://artifactregistry.googleapis.com/v1/projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts {"name":"projects/safehaven-463909/locations/us-central1/repositories/gcf-artifacts","format":"DOCKER","description":"This repository is created and used by Cloud Functions for storing function docker images.","labels":{"goog-managed-by":"cloudfunctions"},"createTime":"2025-06-25T04:53:46.904560Z","updateTime":"2025-06-27T11:40:52.108431Z","mode":"STANDARD_REPOSITORY","cleanupPolicies":{"firebase-functions-cleanup":{"id":"firebase-functions-cleanup","action":"DELETE","condition":{"tagState":"ANY","olderThan":"345600s"}}},"sizeBytes":"588744991","vulnerabilityScanningConfig":{"lastEnableTime":"2025-06-25T04:53:38.819449109Z","enablementState":"SCANNING_DISABLED","enablementStateReason":"API containerscanning.googleapis.com is not enabled."},"satisfiesPzi":true,"registryUri":"us-central1-docker.pkg.dev/safehaven-463909/gcf-artifacts"}
[debug] [2025-06-27T11:41:57.867Z] Functions deploy failed.
[debug] [2025-06-27T11:41:57.867Z] {
  "endpoint": {
    "id": "sendAlertNotifications",
    "project": "safehaven-463909",
    "region": "us-central1",
    "entryPoint": "sendAlertNotifications",
    "platform": "gcfv2",
    "runtime": "nodejs18",
    "eventTrigger": {
      "eventType": "google.firebase.database.ref.v1.created",
      "retry": false,
      "eventFilters": {},
      "eventFilterPathPatterns": {
        "ref": "alerts/{alertId}",
        "instance": "*"
      },
      "region": "us-central1"
    },
    "labels": {
      "deployment-tool": "cli-firebase"
    },
    "serviceAccount": null,
    "ingressSettings": null,
    "availableMemoryMb": null,
    "timeoutSeconds": null,
    "maxInstances": null,
    "minInstances": null,
    "concurrency": 80,
    "vpc": null,
    "environmentVariables": {
      "FIREBASE_CONFIG": "{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}",
      "GCLOUD_PROJECT": "safehaven-463909",
      "EVENTARC_CLOUD_EVENT_SOURCE": "projects/safehaven-463909/locations/us-central1/services/sendAlertNotifications"
    },
    "codebase": "default",
    "cpu": 1,
    "targetedByOnly": false,
    "hash": "79ba08d3a36c20435fb4e8602609f090529fede6"
  },
  "op": "create",
  "original": {
    "name": "FirebaseError",
    "children": [],
    "context": {
      "function": "projects/safehaven-463909/locations/us-central1/functions/sendAlertNotifications"
    },
    "exit": 1,
    "message": "Failed to create function projects/safehaven-463909/locations/us-central1/functions/sendAlertNotifications",
    "original": {
      "name": "FirebaseError",
      "children": [],
      "context": {
        "body": {
          "error": {
            "code": 400,
            "message": "Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendalertnotifications-189781: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
            "status": "FAILED_PRECONDITION"
          }
        },
        "response": {
          "statusCode": 400
        }
      },
      "exit": 1,
      "message": "Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=sendAlertNotifications had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendalertnotifications-189781: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
      "status": 400
    },
    "status": 400,
    "code": 400
  }
}
[debug] [2025-06-27T11:41:57.867Z] {
  "endpoint": {
    "id": "handleSOSRequest",
    "project": "safehaven-463909",
    "region": "us-central1",
    "entryPoint": "handleSOSRequest",
    "platform": "gcfv2",
    "runtime": "nodejs18",
    "eventTrigger": {
      "eventType": "google.cloud.firestore.document.v1.created",
      "retry": false,
      "eventFilters": {
        "database": "(default)",
        "namespace": "(default)"
      },
      "eventFilterPathPatterns": {
        "document": "sosMessages/{sosId}"
      },
      "region": "nam5"
    },
    "labels": {
      "deployment-tool": "cli-firebase"
    },
    "serviceAccount": null,
    "ingressSettings": null,
    "availableMemoryMb": null,
    "timeoutSeconds": null,
    "maxInstances": null,
    "minInstances": null,
    "concurrency": 80,
    "vpc": null,
    "environmentVariables": {
      "FIREBASE_CONFIG": "{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}",
      "GCLOUD_PROJECT": "safehaven-463909",
      "EVENTARC_CLOUD_EVENT_SOURCE": "projects/safehaven-463909/locations/us-central1/services/handleSOSRequest"
    },
    "codebase": "default",
    "cpu": 1,
    "targetedByOnly": false,
    "hash": "79ba08d3a36c20435fb4e8602609f090529fede6"
  },
  "op": "create",
  "original": {
    "name": "FirebaseError",
    "children": [],
    "context": {
      "function": "projects/safehaven-463909/locations/us-central1/functions/handleSOSRequest"
    },
    "exit": 1,
    "message": "Failed to create function projects/safehaven-463909/locations/us-central1/functions/handleSOSRequest",
    "original": {
      "name": "FirebaseError",
      "children": [],
      "context": {
        "body": {
          "error": {
            "code": 400,
            "message": "Validation failed for trigger projects/safehaven-463909/locations/nam5/triggers/handlesosrequest-930208: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
            "status": "FAILED_PRECONDITION"
          }
        },
        "response": {
          "statusCode": 400
        }
      },
      "exit": 1,
      "message": "Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=handleSOSRequest had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/nam5/triggers/handlesosrequest-930208: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
      "status": 400
    },
    "status": 400,
    "code": 400
  }
}
[debug] [2025-06-27T11:41:57.867Z] {
  "endpoint": {
    "id": "sendBulkSMSAlert",
    "project": "safehaven-463909",
    "region": "us-central1",
    "entryPoint": "sendBulkSMSAlert",
    "platform": "gcfv2",
    "runtime": "nodejs18",
    "eventTrigger": {
      "eventType": "google.firebase.database.ref.v1.created",
      "retry": false,
      "eventFilters": {},
      "eventFilterPathPatterns": {
        "ref": "alerts/{alertId}",
        "instance": "*"
      },
      "region": "us-central1"
    },
    "labels": {
      "deployment-tool": "cli-firebase"
    },
    "serviceAccount": null,
    "ingressSettings": null,
    "availableMemoryMb": null,
    "timeoutSeconds": null,
    "maxInstances": null,
    "minInstances": null,
    "concurrency": 80,
    "vpc": null,
    "environmentVariables": {
      "FIREBASE_CONFIG": "{\"projectId\":\"safehaven-463909\",\"databaseURL\":\"https://safehaven-463909-default-rtdb.firebaseio.com\",\"storageBucket\":\"safehaven-463909.firebasestorage.app\"}",
      "GCLOUD_PROJECT": "safehaven-463909",
      "EVENTARC_CLOUD_EVENT_SOURCE": "projects/safehaven-463909/locations/us-central1/services/sendBulkSMSAlert"
    },
    "codebase": "default",
    "cpu": 1,
    "targetedByOnly": false,
    "hash": "79ba08d3a36c20435fb4e8602609f090529fede6"
  },
  "op": "create",
  "original": {
    "name": "FirebaseError",
    "children": [],
    "context": {
      "function": "projects/safehaven-463909/locations/us-central1/functions/sendBulkSMSAlert"
    },
    "exit": 1,
    "message": "Failed to create function projects/safehaven-463909/locations/us-central1/functions/sendBulkSMSAlert",
    "original": {
      "name": "FirebaseError",
      "children": [],
      "context": {
        "body": {
          "error": {
            "code": 400,
            "message": "Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendbulksmsalert-404702: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
            "status": "FAILED_PRECONDITION"
          }
        },
        "response": {
          "statusCode": 400
        }
      },
      "exit": 1,
      "message": "Request to https://cloudfunctions.googleapis.com/v2/projects/safehaven-463909/locations/us-central1/functions?functionId=sendBulkSMSAlert had HTTP Error: 400, Validation failed for trigger projects/safehaven-463909/locations/us-central1/triggers/sendbulksmsalert-404702: Invalid resource state for \"\": Permission denied while using the Eventarc Service Agent. If you recently started to use Eventarc, it may take a few minutes before all necessary permissions are propagated to the Service Agent. Otherwise, verify that it has Eventarc Service Agent role.",
      "status": 400
    },
    "status": 400,
    "code": 400
  }
}
[error] Error: There was an error deploying functions:
[error] - Error Failed to create function sendAlertNotifications in region us-central1
[error] - Error Failed to create function handleSOSRequest in region us-central1
[error] - Error Failed to create function sendBulkSMSAlert in region us-central1
